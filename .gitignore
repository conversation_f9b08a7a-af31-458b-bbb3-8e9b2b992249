# Environment variables
.env

# Virtual environments
venv/
env/
ENV/
.venv/
.env/
backend/venv/
backend/env/

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

# Generated files
backend/generated_ads/
backend/uploads/
uploads/
*test*
AGENTS.md
.augment-guidelines
CLAUDE.md