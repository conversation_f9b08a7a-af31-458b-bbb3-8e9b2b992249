# Backend Development

- for anything related to backend folder i.e. `backend/`

## Build & Run Commands

**Environment Setup (with uv):**
- Create virtual environment & install dependencies: `cd backend && uv venv && uv pip install -e .`

**Local Server:**
- Run dev server with auto-reload: `cd backend && uvicorn server.main:app --reload`
- Prod server: `cd backend && gunicorn -k uvicorn.workers.UvicornWorker server.main:app`
- Run the command-line interface: `cd backend && python cli/main.py --help`
- **Workspace sync**: `make install-dev` or `uv sync --all-extras`
- **Python (server/cli)**: `make lint` (flake8 + mypy), `make format` (black + isort), `make test` (pytest)


## **Containers / Services:**
- Start stack: `cd backend/infra && docker compose up -d`
- Stop stack: `docker compose down -v`

## Code Style Guidelines

**Python Backend:**
- Use **FastAPI** with **Pydantic** models for all API endpoints and data validation.
- Use **LangGraph** for defining and orchestrating all LLM workflows.
- **Code Organization:**
  - `server/routes/`: API endpoint definitions.
  - `server/workflows/`: High-level workflow orchestration.
  - `server/graph/nodes/`: Individual, reusable graph nodes.
  - `server/prompts/`: All LLM prompt templates reside here exclusively.
  - `server/tools/`: All agent tools.
- **Strict Rules:**
  - No file should exceed **300 lines** of code. Refactor large files into smaller modules.
  - Every new object, state, or node gets its own separate module/file.
  - Use `async/await` for all I/O-bound operations (e.g., API calls, LLM requests).
- **Import Order:**
  1. Standard library imports (e.g., `os`, `sys`).
  2. Third-party imports (e.g., `fastapi`, `langchain`).
  3. Local application imports (e.g., `from server.utils import ...`).
- **Typing:** Use type hints for all function signatures and variables.
- Python 3.12+, FastAPI, uvicorn; full type hints & Google-style docstrings.
- Handle config & secrets via `pydantic.BaseSettings`; load from env / secret store.
- Wrap LLM and graph calls with LangFuse traces; stream tokens when possible.
- **Naming**: snake_case (Python), camelCase (TS), PascalCase (components/classes)
- **Error handling**: Use proper exception types, async/await patterns, structured logging

Follow these rules to keep the codebase small, modular, and easy to reason about. Happy hacking!