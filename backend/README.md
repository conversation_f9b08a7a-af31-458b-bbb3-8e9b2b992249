# ShoshinAI Backend

FastAPI-based backend service for ShoshinAI.

## Quick Start

1. Install dependencies using uv:
```bash
cd backend
uv venv
uv pip install -e .
```

2. Copy environment variables:
```bash
cp .env.example .env
```

3. Run the development server:
```bash
uvicorn server.main:app --reload
```

The API will be available at http://localhost:8000
API documentation at http://localhost:8000/docs

## Project Structure

```
backend/
├── server/
│   ├── routes/         # API endpoints
│   ├── workflows/      # LangGraph workflows
│   ├── graph/         # Graph nodes and state
│   ├── prompts/       # LLM prompts
│   ├── tools/         # Agent tools
│   ├── models/        # Pydantic models
│   ├── utils/         # Utilities
│   └── main.py        # FastAPI app
├── cli/               # CLI application
├── infra/             # Infrastructure configs
└── tests/             # Test files
```

## Available Endpoints

- `GET /` - Root endpoint
- `GET /health` - Health check
- `GET /docs` - API documentation

## Development

- Format code: `make format`
- Lint code: `make lint`
- Run tests: `make test`