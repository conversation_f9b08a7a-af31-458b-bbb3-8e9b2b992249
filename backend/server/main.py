"""
Main FastAPI application module.
"""
from contextlib import asynccontextmanager
from typing import Any

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from server.utils.config import settings
from server.utils.logger import get_logger

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> Any:
    """
    Manage application lifecycle events.
    
    Args:
        app: FastAPI application instance
        
    Yields:
        None
    """
    logger.info("Starting up ShoshinAI server...")
    yield
    logger.info("Shutting down ShoshinAI server...")


app = FastAPI(
    title="ShoshinAI API",
    description="Backend API for ShoshinAI",
    version="0.1.0",
    lifespan=lifespan,
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root() -> JSONResponse:
    """
    Root endpoint.
    
    Returns:
        JSONResponse: Welcome message
    """
    return JSONResponse(
        content={
            "message": "Welcome to ShoshinAI API",
            "version": "0.1.0",
            "docs": "/docs",
        }
    )


@app.get("/health")
async def health_check() -> JSONResponse:
    """
    Health check endpoint.
    
    Returns:
        JSONResponse: Health status
    """
    return JSONResponse(
        content={
            "status": "healthy",
            "service": "shoshinai-api",
        }
    )