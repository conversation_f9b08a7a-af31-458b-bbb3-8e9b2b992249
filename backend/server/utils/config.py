"""
Configuration settings using Pydantic BaseSettings.
"""
from typing import List

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.
    """
    
    # Application settings
    app_name: str = Field(default="ShoshinAI", description="Application name")
    debug: bool = Field(default=False, description="Debug mode")
    
    # CORS settings
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000"],
        description="Allowed CORS origins"
    )
    
    # Database settings
    database_url: str = Field(
        default="postgresql://user:password@localhost/shoshinai",
        description="Database connection URL"
    )
    
    # Redis settings
    redis_url: str = Field(
        default="redis://localhost:6379",
        description="Redis connection URL"
    )
    
    # API Keys and Secrets
    secret_key: str = Field(
        default="your-secret-key-here",
        description="Secret key for JWT tokens"
    )
    
    # LLM settings
    openai_api_key: str = Field(
        default="",
        description="OpenAI API key"
    )
    
    # LangFuse settings
    langfuse_public_key: str = Field(
        default="",
        description="LangFuse public key"
    )
    langfuse_secret_key: str = Field(
        default="",
        description="LangFuse secret key"
    )
    langfuse_host: str = Field(
        default="https://cloud.langfuse.com",
        description="LangFuse host URL"
    )
    
    class Config:
        """Pydantic config."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Create global settings instance
settings = Settings()